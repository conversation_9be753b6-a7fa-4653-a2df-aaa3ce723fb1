<template>
  <data-form-editor
    ref="formEditor"
    class="page-jobs"
    :title="$t('dialog.postTitle')"
    :tableName="dataTable.name"
    :data="dataTable.body"
    :column="dthead"
    :getNewData="getNewData"
    :getFormRef="() => $refs.jobDataEditorForm"
    @row-delete="onDelete"
    @row-update="onUpdate"
    @row-new="onNew"
  >
    <template #form="{ formData }">
      <el-form
        ref="jobDataEditorForm"
        :model="formData"
        :label-width="formLabelWidth"
        :rules="rules"
        class="grid grid-cols-1"
      >
        <el-form-item prop="titleName">
          <template #label>
            <EllipsisText :content="$t('dialog.postName')" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput v-model="formData.titleName" :maxlength="16" style="height: 50px;" />
        </el-form-item>
        <el-form-item prop="titleSortValue">
          <template #label>
            <EllipsisText :content="$t('dialog.sortValue')" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfNumberInput
            v-model="formData.titleSortValue"
            :placeholder="$t('msgbox.sortAdvice')"
            :min="-2147483648"
            :max="2147483648"
            :controls="false"
            style="height: 50px;"
          />
        </el-form-item>
        <el-form-item prop="note">
          <template #label>
            <EllipsisText :content="$t('dialog.notes')" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput
            v-model="formData.note"
            type="textarea"
            resize="none"
            :rows="3"
            :maxlength="256"
          />
        </el-form-item>
      </el-form>
    </template>
  </data-form-editor>
</template>

<script>
import dbCmd from '@/modules/protocol/db.pb.cmd'
import bfutil from '@/utils/bfutil'
import vueMixin from '@/utils/vueMixin'
import { v1 as uuid } from 'uuid'
import bfproto from '@/modules/protocol'
import bfNotify from '@/utils/notify'

import DataFormEditor from '@/components/common/DataFormEditor.vue'
import BfInput from '@/components/bfInput/main'
import EllipsisText from '@/components/common/EllipsisText.vue'
import BfNumberInput from '@/components/bfNumberInput/main'

const dbSubject = `db.${bfglob.sysId}`

export default {
  name: 'BfJobs',
  mixins: [vueMixin],
  data() {
    const allData = bfglob.gjobsData.getAll()

    return {
      allData,
      dataTable: {
        name: 'jobsTable',
        body: bfutil.objToArray(allData),
      },
      defaultData: {
        rid: '',
        titleName: '',
        titleSortValue: 100,
        note: '',
      },
    }
  },
  methods: {
    async onDelete(row) {
      await this.delete_job_data(row, dbCmd.DB_USER_TITLE_DELETE)
    },
    async onUpdate(row, done) {
      const isOk = await this.update_job_data(row, dbCmd.DB_USER_TITLE_UPDATE)
      if (!isOk) return
      done()
    },
    // addNewCb：存在这个回调函数则需要继续添加新的一行
    async onNew(row, done, addNewCb) {
      const isOk = await this.add_job_data(row, dbCmd.DB_USER_TITLE_INSERT)
      if (!isOk) return
      if (addNewCb) {
        // 重置标签页数据
        bfutil.resetForm(this, 'jobDataEditorForm')
        addNewCb(row)
        return
      }
      done()
    },
    // 返回一个新的默认参数对象
    getNewData() {
      return { ...this.defaultData }
    },

    add_job_data(data, add_cmd) {
      const msgObj = {
        ...data,
        rid: uuid(),
      }

      return bfproto
        .sendMessage(add_cmd, msgObj, 'db_user_title', dbSubject)
        .then(rpc_cmd_obj => {
          bfglob.console.log('add job res:', rpc_cmd_obj)
          if (rpc_cmd_obj.resInfo === '+OK') {
            bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
            bfglob.emit('add_global_jobData', msgObj)

            // 添加查询日志
            const note =
              this.$t('dialog.add') +
              msgObj.titleName +
              this.$t('msgbox.postData')
            bfglob.emit('addnote', note)
          } else {
            if (rpc_cmd_obj.resInfo.includes('db_user_title_title_name_key')) {
              bfNotify.warningBox(this.$t('msgbox.repeatPostName'))
            } else {
              bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
            }
          }
          return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
        })
        .catch(err => {
          bfglob.console.warn('add job timeout:', err)
          return Promise.resolve(false)
        })
    },
    update_job_data(data, up_db_cmd) {
      const msgObj = {
        ...data,
      }

      return bfproto
        .sendMessage(up_db_cmd, msgObj, 'db_user_title', dbSubject)
        .then(rpc_cmd_obj => {
          bfglob.console.log('update job res:', rpc_cmd_obj)
          if (rpc_cmd_obj.resInfo === '+OK') {
            bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')

            // 更新全局组织机构数据
            bfglob.emit('update_global_jobData', msgObj)

            // 添加查询日志
            const note =
              this.$t('dialog.update') +
              msgObj.titleName +
              this.$t('msgbox.postData')
            bfglob.emit('addnote', note)
          } else {
            if (rpc_cmd_obj.resInfo.includes('db_user_title_title_name_key')) {
              bfNotify.warningBox(this.$t('msgbox.repeatPostName'))
            } else {
              bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
            }
          }

          return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
        })
        .catch(err => {
          bfglob.console.warn('update job timeout:', err)
          bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
          return Promise.resolve(false)
        })
    },
    delete_job_data(data, del_cmd) {
      return bfproto
        .sendMessage(del_cmd, data, 'db_user_title', dbSubject)
        .then(rpc_cmd_obj => {
          bfglob.console.log('delete job res:', rpc_cmd_obj)

          if (rpc_cmd_obj.resInfo === '+OK') {
            bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
            bfglob.emit('delete_global_jobData', data)

            // 添加查询日志
            const note =
              this.$t('dialog.delete') +
              data.titleName +
              this.$t('msgbox.postData')
            bfglob.emit('addnote', note)
          } else {
            bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
          }

          return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
        })
        .catch(err => {
          bfglob.console.warn('delete job timeout:', err)
          bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
          return Promise.resolve(false)
        })
    },
    // 同步dataTable数据
    upsetDataTableBody() {
      this.dataTable.body = bfutil.objToArray(bfglob.gjobsData.getAll())
    },
  },
  components: {
    DataFormEditor,
    BfInput,
    EllipsisText,
    BfNumberInput,
  },
  computed: {
    isFR() {
      return this.$i18n.locale === 'fr'
    },
    formLabelWidth() {
      return this.isFR ? '130px' : '80px'
    },
    dthead() {
      return [
        {
          title: this.$t('dialog.postName'),
          data: 'titleName',
          width: '150px',
        },
        {
          title: this.$t('dialog.sortValue'),
          data: 'titleSortValue',
          width: '80px',
        },
        {
          title: this.$t('dialog.notes'),
          data: 'note',
          width: '100px',
        },
      ]
    },
    rules() {
      return {
        titleName: [
          {
            required: true,
            message: this.$t('dialog.requiredRule'),
            trigger: 'blur',
          },
        ],
      }
    },
  },
  watch: {
    allData: {
      deep: true,
      handler(val) {
        //todo 删除时没有触发监听器,导致表格没有重新渲染
        this.dataTable.body = bfutil.objToArray(val)
      },
    },
  },
  mounted() {
    bfglob.on('add_global_jobData', this.upsetDataTableBody)
    bfglob.on('update_global_jobData', this.upsetDataTableBody)
    bfglob.on('delete_global_jobData', this.upsetDataTableBody)
  },
}
</script>

<style></style>
