<template>
  <bf-dialog
    v-model="dlgVisible"
    :title="$t('nav.authorization')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    center
    class="auth-info"
    modal-class="dialog-modal-mask"
    @closed="closeDlgFn"
  >
    <!-- Form to request a new license -->
    <template v-if="isRequestAuth">
      <el-form ref="requestLicenseForm" :model="licData" :rules="licDataRules" label-position="right" label-width="100px" class="mt-2 w-full">
        <el-form-item prop="projName" class="mb-3">
          <template #label>
            <EllipsisText :content="$t('auth.projName') + ':'" class="h-[50px] leading-[50px]" :max-width="110" />
          </template>
          <BfInput v-model="licData.projName" :maxlength="16" class="input-h-50 w-[380px]" />
        </el-form-item>
        <el-form-item class="mb-3">
          <template #label>
            <EllipsisText :content="$t('auth.authorizedModules') + ':'" :max-width="110" />
          </template>
          <div class="pt-[5px] text-base leading-6">
            <template v-for="(val, key) in moduleList" :key="key">
              <BfCheckbox v-model="licData.lic8100Content[key]" :true-value="1" :false-value="4">
                <EllipsisText :content="$t(getAuthModuleI18nKey(val))" class="m-0 max-w-[130px]" />
              </BfCheckbox>
            </template>
          </div>
        </el-form-item>
        <el-form-item class="mb-3">
          <template #label>
            <span class="text-base font-medium text-white">{{ $t('auth.authorizedNumber') + ':' }}</span>
          </template>
          <div v-for="(val, key) in maxModuleList" :key="key" class="my-1.5 mr-[12px] flex items-center justify-between gap-1">
            <EllipsisText :content="$t(getAuthModuleI18nKey(val))" class="max-w-[60px] !w-fit text-base font-medium text-white/50 leading-[50px] m-0" />
            <BfNumberInput
              v-model="licData.lic8100Content[key]"
              :min="0"
              :max="2147483647"
              :controls="false"
              :placeholder="$t('auth.unlimited')"
              class="input-h-50"
            />
          </div>
        </el-form-item>
        <el-form-item prop="note" class="mb-3">
          <template #label>
            <EllipsisText :content="$t('auth.authDescription') + ':'" :max-width="110" />
          </template>
          <BfInput v-model="licData.note" :maxlength="256" type="textarea" :rows="3" :autosize="{ minRows: 2, maxRows: 5 }" resize="none" class="w-[380px]" />
        </el-form-item>
        <div class="my-2 flex justify-center gap-x-10">
          <BfBtn
            width="122px"
            height="43px"
            font-size="16px"
            color="#757575"
            background-color="#7575752D"
            @click="backToAuthInfo"
          >
            {{ $t('dialog.back') }}
          </BfBtn>
          <BfBtn
            width="122px"
            height="43px"
            font-size="16px"
            color="#FDA216"
            background-color="#FDA2162D"
            @click="generateLicense"
          >
            {{ $t('dialog.confirm') }}
          </BfBtn>
        </div>
      </el-form>
    </template>
    <!-- Display existing license information -->
    <template v-else-if="licenseIsExist">
      <div class="mt-2 flex flex-col gap-2">
        <div class="flex items-center">
          <div class="shrink-0 basis-[18%] pr-2.5 text-right text-xl font-medium text-white">
            <EllipsisText :content="$t('auth.projName') + ':'" />
          </div>
          <div class="text-right text-xl font-medium text-white">
            <EllipsisText :content="license.projName" />
          </div>
        </div>

        <template v-if="license.lic">
          <div class="flex items-center">
            <div class="shrink-0 basis-[18%] pr-2.5 text-right text-xl font-medium text-white">
              <EllipsisText :content="$t('auth.expireTime') + ':'" />
            </div>
            <div class="flex items-center text-right text-xl font-medium text-white">
              <EllipsisText :content="expireTimeLabel" />
              <!-- Show remaining time if less than a year -->
              <div v-if="remainingExpirationTime.days <= 365" :class="['ml-2 inline-block', { 'text-red-500': remainingExpirationTime.days <= 30 }]">
                <EllipsisText :content="$t('auth.remainingExpirationTime', remainingExpirationTime)" />
              </div>
            </div>
          </div>

          <div v-if="licenses" class="flex">
            <div class="shrink-0 basis-[18%] pr-2.5 text-right text-xl font-medium text-white">
              <EllipsisText :content="$t('auth.authorizedContent') + ':'" class="max-w-[130px]" />
            </div>
            <div class="license-content grid grid-flow-col grid-cols-2 grid-rows-6 gap-2 text-left text-base font-medium">
               <BfCheckbox v-for="(val, key) in licenses" :key="key" :model-value="val.value" class="!h-7">
                  <EllipsisText :content="getLicenseItemText(key, val)" class="m-0 max-w-[168px]"/>
              </BfCheckbox>
            </div>
          </div>
        </template>
      </div>
      <div class="my-2 flex justify-center gap-x-10">
        <BfBtn
          width="122px"
          height="43px"
          font-size="16px"
          color="#FDA216"
          background-color="#FDA2162D"
          @click="jumpToRequestLicense(true)"
        >
          <EllipsisText :content="$t('auth.applyAuth')" class="m-0 max-w-[110px]" />
        </BfBtn>
        <BfBtn
          width="122px"
          height="43px"
          font-size="16px"
          color="#60ACDA"
          background-color="#60ACDA29"
          @click="$refs.licenseFileInput.click()"
        >
          <EllipsisText :content="$t('auth.importAuthFile')" class="m-0 max-w-[110px]" />
          <input ref="licenseFileInput" type="file" hidden accept=".lic" @change="evt => importAndUploadLicense(evt.target.files)" />
        </BfBtn>
      </div>
    </template>
    <!-- Display "no authorization" message -->
    <template v-else>
      <div class="flex h-48 items-center justify-center text-xl text-white">
        <EllipsisText :content="$t('auth.noAuthAlert')" />
      </div>
      <div class="my-2 text-center">
        <BfBtn
          width="122px"
          height="43px"
          font-size="16px"
          color="#FDA216"
          background-color="rgba(253, 162, 22, 0.17)"
          box-shadow="inset 0px -8px 6px rgba(255, 74, 74, 0.14)"
          @click="jumpToRequestLicense(false)"
        >
          {{ $t('auth.applyAuth') }}
        </BfBtn>
      </div>
    </template>
  </bf-dialog>
</template>

<script>
  // Script content remains the same
  import vueMixin from '@/utils/vueMixin'
  import {
    calcRemainingExpirationTime,
    getAuthModuleI18nKey,
    getLicense,
    isAuthData,
    isAuthForEver,
    isAuthModule,
    licenseIsExist,
    LicenseApplicationModuleNames,
    LicenseModuleNames,
    requestCurrentLicense,
    requestGenerateLicense,
    saveLicense,
    uploadLicenseFile,
  } from '@/utils/bfAuth'
  import { messageBox } from '@/utils/notify'
  import { utcToLocalTime, DateMask } from '@/utils/time'
  import validateRules from '@/utils/validateRules'
  import bfutil from '@/utils/bfutil'
  import dayjs from 'dayjs'
  import bfproto, { bfdxProtoPackageName } from '@/modules/protocol'
  import base64js from 'base64-js'
  import bfDialog from '@/components/bfDialog/main'
  import BfCheckbox from '@/components/bfCheckbox/main'
  import BfBtn from '@/components/bfButton'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import BfInput from '@/components/bfInput/main'
  import BfNumberInput from '@/components/bfNumberInput/main'

  export default {
    name: 'BfAuthorization',
    components: {
      BfCheckbox,
      BfBtn,
      bfDialog,
      EllipsisText,
      BfInput,
      BfNumberInput,
    },
    mixins: [vueMixin],
    props: {
      dialogVisible: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['update:dialogVisible'],
    data() {
      return {
        license: {},
        isRequestAuth: false,
        licData: {
          projName: '',
          note: '',
          lic8100Content: {
            modRfid: 4,
            modRecord: 4,
            modPhoneGateway: 4,
            modDispatch: 4,
            modTraditionalDmr: 4,
            modSvt: 4,
            maxControllers: undefined,
            maxDevices: undefined,
            maxUsers: undefined,
          },
        },
      }
    },
    computed: {
      dlgVisible: {
        get() {
          return this.dialogVisible !== undefined ? this.dialogVisible : this.visible
        },
        set(val) {
          if (this.dialogVisible !== undefined) {
            this.$emit('update:dialogVisible', val)
          } else {
            this.visible = val
          }
        },
      },
      moduleList() {
        return Object.keys(LicenseApplicationModuleNames)
          .filter(key => isAuthModule(LicenseApplicationModuleNames[key]))
          .map(key => ({ [key]: LicenseApplicationModuleNames[key] }))
          .reduce((p, c) => Object.assign(p, c), {})
      },
      maxModuleList() {
        return Object.keys(LicenseApplicationModuleNames)
          .filter(key => !isAuthModule(LicenseApplicationModuleNames[key]))
          .map(key => ({ [key]: LicenseApplicationModuleNames[key] }))
          .reduce((p, c) => Object.assign(p, c), {})
      },
      licenses() {
        const licenses = this.license.lic?.licenses
        if (!licenses) {
          return undefined
        }

        return Object.values(LicenseModuleNames)
          .map(key => {
            const isData = isAuthData(key)
            return {
              [key]: {
                value: isData || licenses[key] === 0 || licenses[key] === 1,
                limit: isData ? (licenses[key] ?? 0) : -1,
              },
            }
          })
          .reduce((p, c) => Object.assign(p, c), {})
      },
      expireTimeLabel() {
        const expireTime = this.license.lic.expireTime
        if (isAuthForEver(expireTime)) {
          return this.$t('auth.permanent')
        }
        return utcToLocalTime(expireTime, DateMask)
      },
      remainingExpirationTime() {
        return calcRemainingExpirationTime(this.license.lic.expireTime)
      },
      licenseIsExist() {
        return licenseIsExist(this.license)
      },
      licDataRules() {
        return {
          projName: [validateRules.required()],
          note: [],
        }
      },
    },
    methods: {
       getLicenseItemText(key, val) {
        let text = this.$t(this.getAuthModuleI18nKey(key));
        if (this.isAuthData(key)) {
          text += ': ';
          if (val.limit > 0) {
            text += val.limit;
          } else {
            text += this.$t('auth.unlimited');
          }
        }
        return text;
      },
      closeDlgFn() {
        setTimeout(() => {
          this.isRequestAuth = false
        }, 350)
      },
      isAuthModule,
      isAuthData,
      getAuthModuleI18nKey,
      syncLicense(lic) {
        this.license = { ...lic }
      },
      async requestLicense(silent = false) {
        const lic = await requestCurrentLicense()
        if (licenseIsExist(lic)) {
          this.syncLicense(lic)
          saveLicense(lic)
        } else {
          !silent && messageBox(this.$t('auth.queryAuthFailed'))
        }
      },
      async generateLicense() {
        const valid = await this.$refs.requestLicenseForm.validate().catch(() => false)
        if (!valid) {
          return
        }

        const lic8100Content = {
          ...this.licData.lic8100Content,
          maxControllers: this.licData.lic8100Content.maxControllers ?? 0,
          maxDevices: this.licData.lic8100Content.maxDevices ?? 0,
          maxUsers: this.licData.lic8100Content.maxUsers ?? 0,
        }
        const contentType = bfproto.bfdx_proto_msg_T('lic_8100_content', bfdxProtoPackageName)
        const content = contentType.encode(contentType.create(lic8100Content)).finish()

        const licRequestType = bfproto.bfdx_proto_msg_T('lic_request', bfdxProtoPackageName)
        const licRequestData = base64js.fromByteArray(licRequestType.encode(licRequestType.create({ userLicContent: content })).finish())
        const licRequestFile = await requestGenerateLicense({
          projName: this.licData.projName,
          note: this.licData.note,
          data: licRequestData,
        })
        if (licRequestFile) {
          messageBox(this.$t('auth.requestAuthSuccess'))
          this.requestLicense(true)
          const timeSuffix = dayjs().format('YYYYMMDDHHmmss')
          bfutil.saveAsFile(`${this.licData.projName}-${timeSuffix}.req`, licRequestFile.buffer)
        }
      },
      importAndUploadLicense(files) {
        console.log('importAndUploadLicense:', files)
        const reader = new FileReader()
        reader.onload = async () => {
          const ok = await uploadLicenseFile(new Uint8Array(reader.result))
          if (ok) {
            messageBox(this.$t('msgbox.importSuccess'))
            this.requestLicense(true)
            this.backToAuthInfo()
          }
        }
        reader.readAsArrayBuffer(files[0])
        this.$nextTick(() => {
          this.$refs.licenseFileInput.value = ''
        })
      },
      jumpToRequestLicense(isRenewal = false) {
        this.isRequestAuth = true
        if (isRenewal) {
          this.licData.projName = this.license.projName
        }
      },
      backToAuthInfo() {
        this.isRequestAuth = false
        this.licData.projName = ''
        this.licData.note = ''
      },
    },
    beforeMount() {
      const lic = getLicense()
      if (licenseIsExist(lic)) {
        this.syncLicense(lic)
      }
      this.requestLicense(true)
    },
  }
</script>

<style lang="scss">
  @use '@/css/common.scss' as *;

  .el-dialog.auth-info {
    width: 592px;
    min-height: 544px;
    height: fit-content;

    .el-dialog__body {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin: 10px;
      padding: 10px;
      background: rgba(6, 121, 204, 0.46);
      box-shadow: inset 0 0 1.125rem rgba(14, 190, 255, 0.74);
      clip-path: polygon(0 0, 100% 0, 100% calc(100% - 30px), calc(100% - 30px) 100%, 0 100%);

      .el-form-item label {
        height: 50px;
        line-height: 50px;
      }
    }

    // Helper class to set the height of element-plus input wrappers.
    // This is difficult to do with Tailwind alone without `!important` or complex selectors.
    .input-h-50 .el-input__wrapper {
      height: 50px;
    }

    // Deep style for el-checkbox labels to allow them to wrap, as the content can be long.
    .license-content .el-checkbox__label {
      white-space: normal;
      word-break: break-word;
    }

    // Default label styles for form items within this component.
    .el-form-item__label {
      font-size: 16px;
      font-weight: 500;
      color: #fff;
    }
  }
</style>