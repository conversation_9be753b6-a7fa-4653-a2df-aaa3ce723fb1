<template>
  <data-form-editor
    ref="formEditor"
    class="page-jobs"
    :title="$t('iot.terminal')"
    :tableName="dataTable.name"
    :data="dataTable.body"
    :column="dthead"
    :getNewData="getNewData"
    :getFormRef="() => $refs.iotDataEditorForm"
    @row-delete="onDelete"
    @row-update="onUpdate"
    @row-new="onNew"
  >
    <template #form="{ formData }">
      <el-form
        ref="iotDataEditorForm"
        :model="formData"
        :label-width="formLabelWidth"
        :rules="rules"
        :validate-on-rule-change="false"
        class="grid grid-cols-1"
      >
        <el-form-item prop="orgId">
          <template #label>
            <EllipsisText :content="$t('dialog.parentOrg')" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfSelect
            v-model="formData.orgId"
            :placeholder="$t('dialog.select')"
            filterable
            :no-match-text="$t('dialog.noMatchText')"
            style="height: 50px;"
          >
            <el-option
              v-for="item in selOrgList"
              :key="item.rid"
              :label="item.label"
              :value="item.rid"
            />
          </BfSelect>
        </el-form-item>
        <el-form-item prop="devName">
          <template #label>
            <EllipsisText :content="$t('dialog.terminalName')" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput v-model="formData.devName" :maxlength="16" style="height: 50px;" />
        </el-form-item>
        <el-form-item prop="devId">
          <template #label>
            <EllipsisText :content="$t('iot.devId')" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput
            v-model="formData.devId"
            :maxlength="10"
            @change="val => devIdToUpperCase(formData, val)"
            style="height: 50px;"
          />
        </el-form-item>
        <el-form-item prop="devType">
          <template #label>
            <EllipsisText :content="$t('dialog.deviceType')" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <el-select
            v-model="formData.devType"
            filterable
            :placeholder="$t('dialog.select')"
            :no-match-text="$t('dialog.noMatchText')"
            style="height: 50px;"
          >
            <el-option
              v-for="(item, index) in devTypeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <template #label>
            <EllipsisText :content="$t('dialog.lngLat')" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <lon-lat v-model:lon="formData.lon" v-model:lat="formData.lat" />
        </el-form-item>
        <el-form-item prop="note">
          <template #label>
            <EllipsisText :content="$t('dialog.notes')" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput
            v-model="formData.note"
            type="textarea"
            resize="none"
            :rows="3"
            :maxlength="256"
          />
        </el-form-item>
      </el-form>
    </template>
  </data-form-editor>
</template>

<script>
import maputil, { showIotMarker } from '@/utils/map'
import { nowUtcTime } from '@/utils/time'
import bfproto from '@/modules/protocol'
import dbCmd from '@/modules/protocol/db.pb.cmd'
import bfutil, { getDbSubject, MapMarkerTypes } from '@/utils/bfutil'
import { IotDeviceTypes } from '@/utils/iot'

import bfNotify from '@/utils/notify'
import validateRules from '@/utils/validateRules'
import { v1 as uuid } from 'uuid'
import vueMixin from '@/utils/vueMixin'
import DataFormEditor from '@/components/common/DataFormEditor.vue'
import { defineAsyncComponent } from 'vue'
import BfInput from '@/components/bfInput/main'
import EllipsisText from '@/components/common/EllipsisText.vue'

const defaultData = {
  rid: '',
  orgId: bfutil.getBaseDataOrgId(),
  devId: '',
  devType: IotDeviceTypes.ThingsCard,
  devName: '',
  note: '',
  lon: '',
  lat: '',
  setting: '{}',
  creator: bfglob.userInfo.rid,
  createTime: '',
}

export default {
  name: 'IOT',
  mixins: [vueMixin],
  data() {
    return {
      dataTable: {
        name: 'iotDevicesTable',
        body: bfutil.objToArray(bfglob.giotDevices.getAll()),
      },
      selOrgList: bfglob.gorgData.getList(),
    }
  },
  methods: {
    devIdToUpperCase(target, value) {
      target.devId = value.padStart(10, '0').toUpperCase()
    },
    async onDelete(row) {
      await this.delete_iotDevice_data(row, dbCmd.DB_IOT_DEVICE_DELETE)
    },
    async onUpdate(row, done) {
      const isOk = await this.update_iotDevice_data(
        row,
        dbCmd.DB_IOT_DEVICE_UPDATE,
      )
      if (!isOk) return
      done()
    },
    // addNewCb：存在这个回调函数则需要继续添加新的一行
    async onNew(row, done, addNewCb) {
      const isOk = await this.add_iotDevice_data(
        row,
        dbCmd.DB_IOT_DEVICE_INSERT,
      )
      if (!isOk) return
      if (addNewCb) {
        // 重置标签页数据
        const __data = this.getNewData()
        __data.devName = bfutil.customNumberIncrement(row.devName)
        bfutil.resetForm(this, 'iotDataEditorForm')
        addNewCb(__data)
        return
      }
      done()
    },
    // 返回一个新的默认参数对象
    getNewData() {
      return { ...defaultData }
    },
    // 公共的错误处理
    checkCommonDbOperationError(resInfo) {
      // ID重复
      if (resInfo.includes('db_iot_device_dev_id_key')) {
        bfNotify.messageBox(this.$t('iot.duplicateID'), 'warning')
        return true
      }
      // 不级单位错误
      if (resInfo.includes('db_iot_device_org_id_fkey')) {
        bfNotify.messageBox(this.$t('iot.orgIdError'), 'warning')
        return true
      }
      return false
    },
    add_iotDevice_data(data, add_cmd) {
      const msgObj = {
        ...data,
        rid: uuid(),
        lon: Number(data.lon),
        lat: Number(data.lat),
        createTime: nowUtcTime(),
      }

      return bfproto
        .sendMessage(add_cmd, msgObj, 'db_iot_device', getDbSubject())
        .then(rpc_cmd_obj => {
          bfglob.console.log('add db_iot_device res:', rpc_cmd_obj)
          if (rpc_cmd_obj.resInfo === '+OK') {
            bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
            bfglob.emit('add_global_db_iot_device', msgObj)

            // 添加marker
            bfglob.giotDevices.setMarker(
              msgObj.rid,
              maputil.mapMarker({
                type: MapMarkerTypes.IotDevice,
                id: msgObj.rid,
                markerName: msgObj.devName,
                className: 'iconfont icon-signal',
                lngLat: [msgObj.lon, msgObj.lat],
                data: msgObj,
              }),
            )
            showIotMarker()

            // 添加查询日志
            const note =
              this.$t('dialog.add') + msgObj.devName + this.$t('iot.terminal')
            bfglob.emit('addnote', note)
          } else {
            if (this.checkCommonDbOperationError(rpc_cmd_obj.resInfo)) {
              return Promise.resolve(false)
            }
            bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
          }
          return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
        })
        .catch(err => {
          bfglob.console.warn('add db_iot_device timeout:', err)
          bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
          return Promise.resolve(false)
        })
    },
    update_iotDevice_data(data, up_cmd) {
      const msgObj = {
        ...data,
        lon: Number(data.lon),
        lat: Number(data.lat),
      }
      return bfproto
        .sendMessage(up_cmd, msgObj, 'db_iot_device', getDbSubject())
        .then(rpc_cmd_obj => {
          bfglob.console.log('update db_iot_device res:', rpc_cmd_obj)
          if (rpc_cmd_obj.resInfo === '+OK') {
            bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')

            // 更新全局组织机构数据
            bfglob.emit('update_global_db_iot_device', msgObj)

            // 添加查询日志
            const note =
              this.$t('dialog.update') +
              msgObj.devName +
              this.$t('iot.terminal')
            bfglob.emit('addnote', note)
          } else {
            if (this.checkCommonDbOperationError(rpc_cmd_obj.resInfo)) {
              return Promise.resolve(false)
            }

            bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
          }
          return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
        })
        .catch(err => {
          bfglob.console.warn('update db_iot_device timeout:', err)
          bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
          return Promise.resolve(false)
        })
    },
    delete_iotDevice_data(data, del_cmd) {
      return bfproto
        .sendMessage(del_cmd, data, 'db_iot_device', getDbSubject())
        .then(rpc_cmd_obj => {
          bfglob.console.log('delete db_iot_device res:', rpc_cmd_obj)

          if (rpc_cmd_obj.resInfo === '+OK') {
            bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
            bfglob.emit('delete_global_db_iot_device', data)

            // 添加查询日志
            const note =
              this.$t('dialog.delete') + data.devName + this.$t('iot.terminal')
            bfglob.emit('addnote', note)
          } else {
            bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
          }
          return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
        })
        .catch(err => {
          bfglob.console.warn('delete db_iot_device timeout:', err)
          bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
          return Promise.resolve(false)
        })
    },
    // 同步dataTable数据
    upsetDataTableBody() {
      this.dataTable.body = bfutil.objToArray(bfglob.giotDevices.getAll())
    },
  },
  components: {
    DataFormEditor,
    BfInput,
    BfSelect,
    EllipsisText,
    LonLat: defineAsyncComponent(
      () => import('@/components/common/lonLat.vue'),
    ),
  },
  computed: {
    isFR() {
      return this.$i18n.locale === 'fr'
    },
    formLabelWidth() {
      return this.isFR ? '130px' : '80px'
    },
    devTypeList() {
      return [
        {
          label: this.$t('iot.thingsCard'),
          value: IotDeviceTypes.ThingsCard,
        },
        {
          label: this.$t('iot.smokeDetector'),
          value: IotDeviceTypes.SmokeDetector,
        },
        {
          label: this.$t('iot.energySavingLamps'),
          value: IotDeviceTypes.EnergySavingLamps,
        },
        {
          label: this.$t('iot.tempDetector'),
          value: IotDeviceTypes.TemperatureHumidityDetector,
        },
      ]
    },
    dthead() {
      return [
        {
          title: this.$t('dialog.parentOrg'),
          data: 'orgId',
          width: '120px',
          render: (data, _, row) => {
            const parent = bfglob.gorgData.get(data || row.orgId)
            return parent?.orgShortName ?? ''
          },
        },
        {
          title: this.$t('dialog.terminalName'),
          data: 'devName',
          width: '150px',
        },
        {
          title: this.$t('iot.devId'),
          data: 'devId',
          width: '120px',
        },
        {
          title: this.$t('dialog.deviceType'),
          data: 'devType',
          width: '120px',
          render: data => {
            const typeOption = this.devTypeList.find(
              item => item.value === data,
            )
            return typeOption?.label ?? data
          },
        },
        {
          title: this.$t('dialog.notes'),
          data: 'note',
          width: '150px',
        },
      ]
    },
    dlgTitle() {
      return this.$t('iot.terminal')
    },
    rules() {
      return {
        orgId: [validateRules.required()],
        devId: [validateRules.required(), validateRules.hex()],
        devName: [validateRules.required()],
        devType: [validateRules.required()],
        lon: [validateRules.required(), validateRules.number()],
        lat: [validateRules.required(), validateRules.number()],
      }
    },
  },
  mounted() {
    bfglob.on('add_global_db_iot_device', this.upsetDataTableBody)
    bfglob.on('update_global_db_iot_device', this.upsetDataTableBody)
    bfglob.on('delete_global_db_iot_device', this.upsetDataTableBody)
  },
}
</script>

<style lang="scss">
.lonLat-wrapper {
  width: 100%;
  display: flex;

  .lon,
  .lat {
    flex: auto;
  }

  .action {
    flex: none;
  }
}
</style>
