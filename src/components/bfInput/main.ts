import { defineComponent, h, computed, ref } from 'vue'
import { ElInput } from 'element-plus'
import 'element-plus/es/components/input/style/css'
import './main.scss'

export default defineComponent({
  name: 'BfInput',
  props: {
    modelValue: {
      type: String,
      default: '',
    },
    ...ElInput.props,
  },
  emits: ['update:modelValue'],
  setup(props: InstanceType<typeof ElInput>['$props'], { emit, slots, expose, attrs }) {
    const inputVal = computed({
      get() {
        return props.modelValue
      },
      set(val) {
        emit('update:modelValue', val)
      },
    })

    const inputProps = computed(() => {
      return {
        ...attrs,
        ...props,
        class: ['bf-input', attrs.class].filter(Boolean).join(' '),
        modelValue: inputVal.value,
        'onUpdate:modelValue': val => {
          inputVal.value = val
        },
      }
    })

    const inputRef = ref<InstanceType<typeof ElInput>>()
    expose({
      inputRef,
    })

    // 使用 h 函数渲染 ElDialog
    return () =>
      h(
        ElInput,
        {
          ...inputProps.value,
          ref: inputRef,
        },
        slots
      )
  },
})
