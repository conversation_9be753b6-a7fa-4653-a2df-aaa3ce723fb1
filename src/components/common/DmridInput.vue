<template>
  <div class="flex flex-nowrap gap-4 generate-dmrId-wrap" :class="{ disabled: disabled, 'is-mobile': isMobile }">
    <bf-number-input
      v-model.number="mmm"
      class="flex-none generate-dmrId-number h-[50px]"
      :size="size"
      :min="minNo"
      :max="maxDmrNo"
      :disabled="disabled"
      @change="mmmChange"
    >
      <template #decrease-icon >
        <span class="iconfont bfdx-jian"></span>
      </template>
      <template #increase-icon>
        <span class="iconfont bfdx-jia"></span>
      </template>
    </bf-number-input>
    <bf-input v-model="dmrIdLabel" class="generate-dmrId-input" :size="size" readonly :suffix-icon="inputSuffixIcon">
      <!-- <template #append>
        <el-button class="generate-dmrId-btn" :disabled="disabled" :size="size" @click="resolveDmrId">
          <span class="iconfont bfdx-shezhi"></span>
        </el-button>
      </template> -->
    </bf-input>
  </div>
</template>

<script>
  import { getDmrMaxNo, ssmmm2dmrid, dmrid2ssmmm, formatDmrIdLabel } from '@/utils/bfutil'
  import bfNumberInput from '@/components/bfNumberInput/main'
  import bfInput from '@/components/bfInput/main'
  import { h } from 'vue'

  export default {
    name: 'DmridInput',
    emits: ['update:modelValue'],
    components: {
      bfNumberInput,
      bfInput,
    },
    props: {
      modelValue: {
        type: [String, Number],
        required: true,
      },
      // 禁用编号输入
      disabled: {
        type: Boolean,
        default: false,
      },
      // 标记dmrId是否为组呼
      isGroup: {
        type: Boolean,
        default: false,
      },
      maxNo: {
        type: Number,
        default: getDmrMaxNo(),
      },
      minNo: {
        type: Number,
        default: 0,
      },
      isDec: {
        type: Boolean,
        default: false,
      },
      size: {
        type: String,
        // default: 'small',
      },
    },
    data() {
      return {
        mmm: this.minNo,
        // 代理商id，默认为0
        salerId: 0,
        // 除了组呼标志位的4字节最高位
        groupBit: 0x7fffffff,
        // 最高19bti的编号
        fullMmmBit: 0x07ffff,
      }
    },
    watch: {
      modelValue: {
        immediate: true,
        handler(val) {
          if (val === '' || val === 0) {
            this.mmmChange(this.mmm)
            return
          }

          if (typeof val === 'number') {
            this.mmm = val & this.fullMmmBit
            return
          }

          const res = dmrid2ssmmm(val)
          this.mmm = res.ok ? res.mmm : this.minNo
        },
      },
    },
    methods: {
      emitDmrId(dmrId) {
        if (this.isDec) {
          // 返回十进制dmrId
          this.$emit('update:modelValue', parseInt(dmrId, 16))
        } else {
          // 返回十六进制dmrId
          this.$emit('update:modelValue', dmrId)
        }
      },
      mmmChange(val) {
        const groupNo = this.isGroup ? 1 : 0
        const dmrId = ssmmm2dmrid(bfglob.sysId, val, groupNo, this.salerId)
        this.emitDmrId(dmrId)
      },
      showResolveFiledMessage() {
        ElMessage({
          message: this.$t('msgbox.resolveDmrIdFailed'),
          type: 'error',
        })
      },
      validator(val) {
        if (this.isGroup) {
          if (val > 0x80ffffff) {
            return false
          }
        } else if (val > 0x00ffffff) {
          return false
        }

        return true
      },
      resolveDmrId() {
        if (this.disabled) {
          return 
        }
        ElMessageBox.prompt(this.$t('msgbox.resolveDmrId'), this.$t('dialog.alertTitle'), {
          confirmButtonText: this.$t('dialog.confirm'),
          cancelButtonText: this.$t('dialog.cancel'),
          inputValidator: val => {
            if (!val) {
              return false
            }
            if (/(^[0-9]*$)|(^0x[0-9a-fA-F]*$)/.test(val)) {
              return this.validator(val)
            } else {
              return false
            }
          },
          inputErrorMessage: this.$t('msgbox.enterCorrectDMRID'),
          closeOnClickModal: false,
          closeOnPressEscape: false,
        })
          .then(({ value }) => {
            try {
              const dmrIdHex = (value & this.groupBit).toString(16).padStart(8, '0')
              const dmrObj = dmrid2ssmmm(dmrIdHex)
              if (!dmrObj.ok || dmrObj.mmm > this.maxDmrNo) {
                this.showResolveFiledMessage()
                return
              }
              this.mmm = dmrObj.mmm
              this.mmmChange(this.mmm)
            } catch (_err) {
              this.showResolveFiledMessage()
            }
          })
          .catch(_e => {})
      },
    },
    computed: {
      isMobile() {
        return this.$root.layoutLevel === 0
      },
      maxDmrNo() {
        return Math.min(this.maxNo, getDmrMaxNo(this.isGroup))
      },
      dmrIdLabel() {
        const dmrId = this.isDec ? this.modelValue.toString(16) : this.modelValue
        return formatDmrIdLabel(dmrId)
      },
      inputSuffixIcon() {
        return h('span', { 
          class: `iconfont bfdx-shezhi generate-dmrId-input-icon  ${this.disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`,
          onClick: this.resolveDmrId
        })
      }
    },
  }
</script>

<style lang="scss">
  .generate-dmrId-wrap {
    width: 100%;
    .generate-dmrId-number {
      .el-input__wrapper {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      .el-input-number__increase, .el-input-number__decrease {
        background-color: transparent;
        border: none;
        span::before {
          font-size: 25px;
          color: unset;
          line-height: 50px;
        }
      }
    }

    .generate-dmrId-input {
      .el-input__wrapper {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;

        span.generate-dmrId-input-icon::before {
          font-size: 25px;
          color: unset;
          line-height: 50px;
        }
      }

      .el-input-group__append {
        background-color: transparent;
      }
    }
  }

  .repeater-write-freq .repeaterCmd .el-form .el-form-item .generate-dmrId-wrap,
  .el-dialog.data-form-dialog .el-form .el-form-item .generate-dmrId-wrap {
    &.is-mobile {
      flex-direction: column;
      gap: 0.35rem;

      .generate-dmrId-number.el-input-number {
        width: 100% !important;
      }
    }
  }
</style>
