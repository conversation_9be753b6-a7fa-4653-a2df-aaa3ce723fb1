import { defineComponent, h, computed, ref } from 'vue'
import { ElInputNumber } from 'element-plus'
import 'element-plus/es/components/input-number/style/css'
import './main.scss'

export default defineComponent({
  name: 'BfInputNumberNew',
  props: {
    modelValue: {
      type: Number,
      default: 0,
    },
    ...ElInputNumber.props,
  },
  emits: ['update:modelValue'],
  setup(props: InstanceType<typeof ElInputNumber>['$props'], { emit, slots, expose, attrs }) {
    const inputVal = computed({
      get() {
        return props.modelValue
      },
      set(val) {
        emit('update:modelValue', val)
      },
    })

    const inputProps = computed(() => {
      return {
        ...attrs,
        ...props,
        class: ['bf-input-number', attrs.class].filter(Boolean).join(' '),
        modelValue: inputVal.value,
        'onUpdate:modelValue': val => {
          inputVal.value = val
        },
      }
    })

    const inputNumberRef = ref<InstanceType<typeof ElInputNumber>>()
    expose({
      inputNumberRef,
    })

    return () =>
      h(
        ElInputNumber,
        {
          ...inputProps.value,
          ref: inputNumberRef,
        },
        slots
      )
  },
})
