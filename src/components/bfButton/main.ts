import { defineComponent, h, computed } from 'vue'
import { ElButton } from 'element-plus'
import './main.scss'

interface BfBtnProps {
  borderWidth?: number
  width?: number
  height?: number
  fontSize?: string
  color: string
  backgroundColor?: string
}

function hexToRgb(hex: string) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null;
}


export default defineComponent({
  name: 'BfBtn',
  props: {
    borderWidth: { type: Number, default: 2 },
    width: { type: [Number, String], default: 200 },
    height: { type: [Number, String], default: 50 },
    fontSize: { type: String, default: '18px' },
    color: { type: String, default: '#FF4A4A' },
    backgroundColor: { type: String, default: 'rgba(117, 117, 117, 0.2)' },
    ...ElButton.props,
  },
  setup(props: BfBtnProps & InstanceType<typeof ElButton>['$props'], { slots, attrs }) {
    
    const pathData = computed(() => {
      const w = typeof props.width === 'number' ? props.width : 200
      const h = typeof props.height === 'number' ? props.height : 50
      const topLeftCut = 8
      const bottomRightCut = 11
      return `M ${topLeftCut},0 L ${w},0 L ${w},${h - bottomRightCut} L ${w - bottomRightCut},${h} L 0,${h} L 0,${topLeftCut} Z`
    })
    
    const buttonProps = computed(() => {
      const {
        borderWidth: _b, width: _w, height: _h, fontSize: _f,
        backgroundColor: _bg, color: _c,
        ...restProps
      } = props
      return { ...attrs, ...restProps }
    })

    const gradientId = `bf-btn-gradient-${Math.random().toString(36).substring(2, 9)}`

    const buttonStyle = computed(() => {
      const rgb = hexToRgb(props.color)
      if (rgb) {
        return {
          boxShadow: `inset 0px -8px 12px rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.16)`
        }
        
      }
      return {}
    })


    return () =>
      h(
        'div',
        {
          class: 'bf-btn-wrapper',
          style: {
            width: typeof props.width === 'number' ? `${props.width}px` : props.width,
            height: typeof props.height === 'number' ? `${props.height}px` : props.height,
            '--main-color': props.color,
            '--font-size': props.fontSize,
          }
        },
        [
          // ---- SVG 层 (保持不变) ----
          h(
            'svg',
            {
              class: 'bf-btn__renderer',
              width: '100%',
              height: '100%',
              viewBox: `0 0 ${typeof props.width === 'number' ? props.width : 200} ${typeof props.height === 'number' ? props.height : 50}`,
              preserveAspectRatio: 'none',
            },
            [
              h('defs', null, [
                h('linearGradient', { id: gradientId, x1: '0%', y1: '100%', x2: '0%', y2: '0%' }, [
                  h('stop', { 'offset': '0%', 'stop-color': props.color, 'stop-opacity': '1' }),
                  h('stop', { 'offset': '100%', 'stop-color': props.color, 'stop-opacity': '0' })
                ])
              ]),
              h('path', {
                d: pathData.value,
                fill: props.backgroundColor,
                stroke: `url(#${gradientId})`,
                'stroke-width': props.borderWidth,
                'vector-effect': 'non-scaling-stroke'
              })
            ]
          ),
          
          // ---- ElButton 内容层 ----
          h(
            ElButton,
            {
              class: 'bf-btn__content',
              ...buttonProps.value,
              style: buttonStyle.value
            },
            slots
          ),
          
          // ---- 角落三角装饰层 ----
          h('i', { class: 'bf-btn__corner top-left' }),
          h('i', { class: 'bf-btn__corner bottom-right' }),
        ]
      )
  },
})