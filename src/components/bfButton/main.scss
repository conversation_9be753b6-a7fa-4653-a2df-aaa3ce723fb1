.bf-btn-wrapper {
  position: relative;
  display: inline-block;
  transition: transform 0.2s ease;
}

.bf-btn-wrapper:active {
  transform: scale(0.98);
}

.bf-btn__renderer,
.bf-btn__content {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
}

.bf-btn__renderer {
  z-index: 1;
  transition: filter 0.3s ease;
  
  filter: drop-shadow(0 4px 8px rgba(var(--main-color, 255, 74, 74), 0.25));
}

.bf-btn__content.el-button {
  z-index: 2;
  
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  
  color: var(--main-color, #FF4A4A) !important;
  font-size: var(--font-size, 18px) !important;
  font-weight: bold !important;

  &:hover,
  &:focus {
    background-color: transparent !important;
    border-color: transparent !important;
    color: var(--main-color, #FF4A4A) !important;
  }
}

.bf-btn__corner {
  position: absolute;
  width: 4px;
  height: 4px;
  z-index: 3;
  transition: filter 0.3s ease;

  background-color: var(--main-color, #FF4A4A);
}

.bf-btn__corner.top-left {
  top: 0;
  left: 0;
  clip-path: polygon(0 0, 100% 0, 0 100%);
}

.bf-btn__corner.bottom-right {
  bottom: 0;
  right: 0;
  clip-path: polygon(100% 100%, 100% 0, 0 100%);
}

.bf-btn-wrapper:hover .bf-btn__renderer {
  filter: drop-shadow(0 6px 12px rgba(var(--main-color, 255, 74, 74), 0.3)) brightness(1.1);
}

.bf-btn-wrapper:hover .bf-btn__corner {
  filter: brightness(1.2);
}